extends Node

# NetworkManager - Autoload для управления сетевыми подключениями
# Поддерживает как клиентские подключения, так и создание dedicated сервера

signal player_connected(id: int)
signal player_disconnected(id: int)
signal connection_failed()
signal connection_succeeded()
signal server_started()
signal server_stopped()

const DEFAULT_PORT = 7000
const MAX_PLAYERS = 100

var multiplayer_peer: ENetMultiplayerPeer
var is_server: bool = false
var is_dedicated_server: bool = false
var connected_players: Dictionary = {}

# Структура данных игрока
class PlayerData:
	var id: int
	var name: String
	var position: Vector2
	var health: float = 100.0
	var level: int = 1
	
	func _init(player_id: int, player_name: String = ""):
		id = player_id
		name = player_name if player_name != "" else "Player_" + str(player_id)

func _ready():
	# Подключаем сигналы мультиплеера
	multiplayer.peer_connected.connect(_on_player_connected)
	multiplayer.peer_disconnected.connect(_on_player_disconnected)
	multiplayer.connected_to_server.connect(_on_connected_to_server)
	multiplayer.connection_failed.connect(_on_connection_failed)
	multiplayer.server_disconnected.connect(_on_server_disconnected)
	
	print("NetworkManager initialized")

# Создание dedicated сервера
func create_server(port: int = DEFAULT_PORT) -> bool:
	multiplayer_peer = ENetMultiplayerPeer.new()
	var error = multiplayer_peer.create_server(port, MAX_PLAYERS)

	if error != OK:
		print("Failed to create server: ", error)
		return false

	multiplayer.multiplayer_peer = multiplayer_peer
	is_server = true
	is_dedicated_server = true

	print("Server created on port: ", port)

	# Создаем данные для сервера как игрока
	var server_player = PlayerData.new(1, "Server")
	server_player.position = Vector2.ZERO
	connected_players[1] = server_player

	server_started.emit()
	return true

# Подключение к серверу как клиент
func connect_to_server(address: String, port: int = DEFAULT_PORT) -> bool:
	multiplayer_peer = ENetMultiplayerPeer.new()
	var error = multiplayer_peer.create_client(address, port)
	
	if error != OK:
		print("Failed to create client: ", error)
		return false
	
	multiplayer.multiplayer_peer = multiplayer_peer
	is_server = false
	
	print("Attempting to connect to: ", address, ":", port)
	return true

# Отключение от сети
func disconnect_from_network():
	print("Disconnecting from network...")

	if multiplayer_peer:
		multiplayer_peer.close()
		multiplayer_peer = null

	multiplayer.multiplayer_peer = null
	connected_players.clear()

	var was_server = is_server
	is_server = false
	is_dedicated_server = false

	print("Disconnected from network")

	if was_server:
		server_stopped.emit()

# Получение данных игрока
func get_player_data(player_id: int) -> PlayerData:
	return connected_players.get(player_id)

# Обновление позиции игрока
@rpc("any_peer", "unreliable")
func update_player_position(player_id: int, position: Vector2):
	if connected_players.has(player_id):
		connected_players[player_id].position = position

# Обновление здоровья игрока
@rpc("any_peer", "reliable")
func update_player_health(player_id: int, health: float):
	if connected_players.has(player_id):
		connected_players[player_id].health = health

# Уведомление о спавне игрока
@rpc("authority", "reliable")
func notify_player_spawned(player_id: int, player_name_str: String, spawn_pos: Vector2):
	print("Received spawn notification for player: ", player_id, " name: ", player_name_str)
	# Уведомляем GameManager о необходимости спавна
	if GameManager.current_state == GameManager.GameState.IN_GAME:
		GameManager.spawn_player(player_id, player_name_str, spawn_pos)

# Синхронизация данных нового игрока
@rpc("authority", "reliable")
func sync_player_data(player_data_dict: Dictionary):
	var player_data = PlayerData.new(player_data_dict.id, player_data_dict.name)
	player_data.position = player_data_dict.position
	player_data.health = player_data_dict.health
	player_data.level = player_data_dict.level
	connected_players[player_data.id] = player_data

# Запрос на присоединение к игре
@rpc("any_peer", "reliable")
func request_join_game(player_name_str: String):
	var sender_id = multiplayer.get_remote_sender_id()

	if is_server:
		print("Player requesting to join: ", player_name_str, " (ID: ", sender_id, ")")

		# Создаем данные нового игрока
		var new_player = PlayerData.new(sender_id, player_name_str)
		new_player.position = Vector2(randf_range(-200, 200), randf_range(-200, 200))
		connected_players[sender_id] = new_player

		# Уведомляем всех игроков (включая нового) о спавне
		for player_id in connected_players:
			notify_player_spawned.rpc_id(player_id, sender_id, player_name_str, new_player.position)

		# Отправляем новому игроку информацию о всех существующих игроках
		for existing_player_id in connected_players:
			if existing_player_id != sender_id:
				var existing_player = connected_players[existing_player_id]
				notify_player_spawned.rpc_id(sender_id, existing_player_id, existing_player.name, existing_player.position)

# Обработчики сигналов
func _on_player_connected(id: int):
	print("Player connected: ", id)
	player_connected.emit(id)

func _on_player_disconnected(id: int):
	print("Player disconnected: ", id)
	connected_players.erase(id)
	player_disconnected.emit(id)

func _on_connected_to_server():
	print("Successfully connected to server")
	connection_succeeded.emit()

func _on_connection_failed():
	print("Failed to connect to server")
	connection_failed.emit()

func _on_server_disconnected():
	print("Server disconnected")
	disconnect_from_network()

# Утилиты
func get_player_count() -> int:
	return connected_players.size()

func is_player_connected(player_id: int) -> bool:
	return connected_players.has(player_id)

func get_all_players() -> Array:
	return connected_players.values()

func get_local_player_id() -> int:
	return multiplayer.get_unique_id()
