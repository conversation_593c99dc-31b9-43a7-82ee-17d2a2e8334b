extends Node

# GameManager - Autoload для управления состоянием игры
# Управляет игровой логикой, состояниями игры и координирует работу с NetworkManager

signal game_state_changed(new_state: GameState)
signal player_spawned(player_id: int)
signal player_despawned(player_id: int)

enum GameState {
	MENU,
	CONNECTING,
	IN_GAME,
	PAUSED,
	DISCONNECTED
}

var current_state: GameState = GameState.MENU
var game_world_scene: PackedScene
var player_scene: PackedScene
var main_menu_scene: PackedScene

# Ссылки на текущие сцены
var current_world: Node
var spawned_players: Dictionary = {}
var local_player: Node

# Настройки игры
var player_name: String = "Stalker"
var spawn_points: Array[Vector2] = [
	Vector2(0, 0),
	Vector2(100, 100),
	Vector2(-100, -100),
	Vector2(200, -200),
	Vector2(-200, 200)
]

func _ready():
	# Загружаем сцены
	game_world_scene = preload("res://scenes/GameWorld.tscn")
	player_scene = preload("res://scenes/Player.tscn")
	main_menu_scene = preload("res://scenes/MainMenu.tscn")

	# Подключаемся к сигналам NetworkManager
	NetworkManager.player_connected.connect(_on_player_connected)
	NetworkManager.player_disconnected.connect(_on_player_disconnected)
	NetworkManager.connection_succeeded.connect(_on_connection_succeeded)
	NetworkManager.connection_failed.connect(_on_connection_failed)
	NetworkManager.server_started.connect(_on_server_started)
	NetworkManager.server_stopped.connect(_on_server_stopped)

	print("GameManager initialized")

	# Проверяем, нужно ли загружать главное меню
	# (главное меню уже загружено как main_scene, если мы не в dedicated режиме)
	var args = OS.get_cmdline_args()
	var is_dedicated = false
	for arg in args:
		if arg == "--server" or arg == "--dedicated-server" or DisplayServer.get_name() == "headless":
			is_dedicated = true
			break

	if not is_dedicated:
		# Мы в клиентском режиме, главное меню уже загружено
		change_game_state(GameState.MENU)

# Смена состояния игры
func change_game_state(new_state: GameState):
	if current_state != new_state:
		print("Game state changed: ", GameState.keys()[current_state], " -> ", GameState.keys()[new_state])
		current_state = new_state
		game_state_changed.emit(new_state)

# Загрузка главного меню
func load_main_menu():
	change_game_state(GameState.MENU)

	# Очищаем текущую сцену
	if current_world:
		current_world.queue_free()
		current_world = null

	# Проверяем, есть ли уже главное меню
	var existing_menu = get_tree().get_first_node_in_group("main_menu")
	if not existing_menu:
		# Загружаем главное меню только если его нет
		var main_menu = main_menu_scene.instantiate()
		get_tree().root.add_child(main_menu)
		print("Main menu loaded")

# Загрузка игрового мира
func load_game_world():
	change_game_state(GameState.IN_GAME)
	
	# Удаляем главное меню
	var main_menu = get_tree().get_first_node_in_group("main_menu")
	if main_menu:
		main_menu.queue_free()
	
	# Создаем игровой мир
	current_world = game_world_scene.instantiate()
	get_tree().root.add_child(current_world)
	
	# Если мы сервер, спавним всех подключенных игроков
	if NetworkManager.is_server:
		for player_data in NetworkManager.get_all_players():
			spawn_player(player_data.id, player_data.name, player_data.position)
	else:
		# Если клиент, запрашиваем присоединение к игре
		NetworkManager.request_join_game.rpc_id(1, player_name)

# Спавн игрока
func spawn_player(player_id: int, player_name: String, spawn_position: Vector2 = Vector2.ZERO):
	if spawned_players.has(player_id):
		print("Player already spawned: ", player_id)
		return
	
	var player_instance = player_scene.instantiate()
	player_instance.name = "Player_" + str(player_id)
	player_instance.set_multiplayer_authority(player_id)
	
	# Настраиваем игрока
	if player_instance.has_method("setup_player"):
		player_instance.setup_player(player_id, player_name)
	
	# Устанавливаем позицию
	if spawn_position == Vector2.ZERO:
		spawn_position = get_random_spawn_point()
	player_instance.global_position = spawn_position
	
	# Добавляем в мир
	if current_world:
		current_world.add_child(player_instance)
		spawned_players[player_id] = player_instance
		
		# Если это локальный игрок, сохраняем ссылку
		if player_id == NetworkManager.get_local_player_id():
			local_player = player_instance
		
		print("Player spawned: ", player_name, " (ID: ", player_id, ")")
		player_spawned.emit(player_id)

# Удаление игрока
func despawn_player(player_id: int):
	if spawned_players.has(player_id):
		var player_instance = spawned_players[player_id]
		player_instance.queue_free()
		spawned_players.erase(player_id)
		
		if local_player == player_instance:
			local_player = null
		
		print("Player despawned: ", player_id)
		player_despawned.emit(player_id)

# Получение случайной точки спавна
func get_random_spawn_point() -> Vector2:
	if spawn_points.size() > 0:
		return spawn_points[randi() % spawn_points.size()]
	return Vector2.ZERO

# Получение локального игрока
func get_local_player() -> Node:
	return local_player

# Получение игрока по ID
func get_player(player_id: int) -> Node:
	return spawned_players.get(player_id)

# Обработчики сигналов NetworkManager
func _on_player_connected(player_id: int):
	print("GameManager: Player connected - ", player_id)
	
	# Если мы в игре и это сервер, спавним нового игрока
	if current_state == GameState.IN_GAME and NetworkManager.is_server:
		var player_data = NetworkManager.get_player_data(player_id)
		if player_data:
			spawn_player(player_id, player_data.name, player_data.position)

func _on_player_disconnected(player_id: int):
	print("GameManager: Player disconnected - ", player_id)
	despawn_player(player_id)

func _on_connection_succeeded():
	print("GameManager: Connected to server successfully")
	change_game_state(GameState.CONNECTING)
	
	# Небольшая задержка перед загрузкой мира
	await get_tree().create_timer(0.5).timeout
	load_game_world()

func _on_connection_failed():
	print("GameManager: Connection failed")
	change_game_state(GameState.DISCONNECTED)
	
	# Возвращаемся в главное меню
	load_main_menu()

func _on_server_started():
	print("GameManager: Server started")
	load_game_world()

	# Спавним игрока сервера (сервер всегда имеет ID = 1)
	var server_id = NetworkManager.get_local_player_id()
	print("Server player ID: ", server_id)
	spawn_player(server_id, player_name, Vector2.ZERO)

func _on_server_stopped():
	print("GameManager: Server stopped")
	change_game_state(GameState.DISCONNECTED)
	
	# Очищаем спавненных игроков
	for player_id in spawned_players.keys():
		despawn_player(player_id)
	
	# Возвращаемся в главное меню
	load_main_menu()

# Утилиты
func get_player_count() -> int:
	return spawned_players.size()

func is_in_game() -> bool:
	return current_state == GameState.IN_GAME

func pause_game():
	if current_state == GameState.IN_GAME:
		change_game_state(GameState.PAUSED)
		get_tree().paused = true

func resume_game():
	if current_state == GameState.PAUSED:
		change_game_state(GameState.IN_GAME)
		get_tree().paused = false

func quit_to_menu():
	NetworkManager.disconnect_from_network()
	load_main_menu()
