# STALZONE ONLINE

Онлайн MMORPG игра в стиле S.T.A.L.K.E.R. с видом сверху, созданная на Godot 4.4.

## Особенности

- **Клиент-серверная архитектура** с поддержкой dedicated server
- **Сетевая синхронизация** игроков в реальном времени
- **Top-down движение** в стиле классических игр
- **Масштабируемая архитектура** для MMORPG
- **Современные практики Godot 4.4** с ENetMultiplayerPeer

## Структура проекта

### Autoload скрипты
- `NetworkManager.gd` - Управление сетевыми подключениями
- `GameManager.gd` - Управление состоянием игры
- `DedicatedServer.gd` - Логика dedicated сервера

### Сцены
- `MainMenu.tscn` - Главное меню с подключением к серверу
- `GameWorld.tscn` - Основной игровой мир
- `Player.tscn` - Персонаж игрока с сетевой синхронизацией

## Запуск игры

### Клиент
1. Запустите проект в Godot Editor или экспортированный клиент
2. В главном меню введите имя игрока
3. Для подключения к серверу введите IP адрес и порт
4. Нажмите "Подключиться"

### Создание локального сервера
1. В главном меню введите имя игрока
2. Выберите порт для сервера (по умолчанию 7000)
3. Нажмите "Создать сервер"

### Dedicated Server
Экспортируйте проект как "Linux Dedicated Server" или "Windows Dedicated Server" и запустите:

```bash
# Linux
./stalzone_server --headless --port 7000 --max-players 100

# Windows
stalzone_server.exe --headless --port 7000 --max-players 100
```

#### Параметры командной строки
- `--port <число>` - Порт сервера (по умолчанию 7000)
- `--max-players <число>` - Максимальное количество игроков (по умолчанию 100)

## Управление

- **WASD** - Движение персонажа
- **ESC** - Выход в главное меню

## Сетевая архитектура

### NetworkManager
- Создание и управление ENetMultiplayerPeer
- Обработка подключения/отключения игроков
- Синхронизация данных игроков
- RPC функции для обмена данными

### GameManager
- Управление состояниями игры (меню, подключение, игра)
- Спавн и деспавн игроков
- Координация с NetworkManager
- Управление сценами

### Player
- Top-down движение с WASD
- Сетевая синхронизация позиции
- Система здоровья
- Интерполяция для плавности движения

## Масштабирование

Архитектура готова для расширения:

1. **Зоны и инстансы** - GameWorld можно разделить на зоны
2. **База данных** - Добавить сохранение персонажей
3. **Инвентарь и предметы** - Система предметов
4. **Квесты и NPC** - Игровой контент
5. **Чат и гильдии** - Социальные функции

## Требования

- Godot 4.4+
- Для dedicated server: Linux или Windows
- Сетевое подключение для мультиплеера

## Разработка

Проект использует современные практики Godot 4.4:
- ENetMultiplayerPeer для сети
- MultiplayerSynchronizer для репликации
- Autoload для глобальных менеджеров
- Сигналы для связи между компонентами

## Лицензия

Проект создан для демонстрации возможностей создания MMORPG на Godot 4.4.
