# STALZONE ONLINE

Онлайн MMORPG игра в стиле S.T.A.L.K.E.R. с видом сверху, созданная на Godot 4.4.

## Особенности

- **Клиент-серверная архитектура** с поддержкой dedicated server
- **Сетевая синхронизация** игроков в реальном времени
- **Top-down движение** в стиле классических игр
- **Масштабируемая архитектура** для MMORPG
- **Современные практики Godot 4.4** с ENetMultiplayerPeer

## Структура проекта

### Autoload скрипты
- `NetworkManager.gd` - Управление сетевыми подключениями
- `GameManager.gd` - Управление состоянием игры
- `DedicatedServer.gd` - Логика dedicated сервера

### Сцены
- `MainMenu.tscn` - Главное меню с подключением к серверу
- `GameWorld.tscn` - Основной игровой мир
- `Player.tscn` - Персонаж игрока с сетевой синхронизацией

## Запуск игры

### ВАЖНО: Настройка Godot
Перед запуском убедитесь, что:
1. Godot 4.4+ установлен
2. Godot добавлен в PATH, ИЛИ скопируйте `godot.exe` в папку проекта

### Способ 1: Через Godot Editor (Рекомендуется)
1. Откройте проект в Godot Editor
2. Нажмите F5 или "Play Project"
3. В главном меню введите имя игрока
4. Нажмите "Создать сервер" для локального сервера

### Способ 2: Через bat-файлы
1. Запустите `start_client.bat` для клиента
2. Запустите `start_server.bat` для dedicated сервера

### Способ 3: Командная строка
```bash
# Клиент
godot

# Dedicated сервер
godot --headless --server --port=7000
```

### Тестирование
Запустите простой тест:
```bash
godot --script test_simple.gd
```

## Управление

- **WASD** - Движение персонажа
- **ESC** - Выход в главное меню
- **F12** - Консоль отладки (в игре)

## Отладка

### Консоль отладки
Нажмите **F12** в игре для открытия консоли отладки. Доступные команды:
- `status` - Показать статус сети
- `players` - Показать подключенных игроков
- `spawn_test` - Создать тестового игрока (только сервер)
- `server` - Создать тестовый сервер на порту 7001
- `connect` - Подключиться к localhost:7001
- `clear` - Очистить консоль

### Тестирование сети
Запустите автоматический тест сети:
```bash
godot --script test_network.gd
```

## Сетевая архитектура

### NetworkManager
- Создание и управление ENetMultiplayerPeer
- Обработка подключения/отключения игроков
- Синхронизация данных игроков
- RPC функции для обмена данными

### GameManager
- Управление состояниями игры (меню, подключение, игра)
- Спавн и деспавн игроков
- Координация с NetworkManager
- Управление сценами

### Player
- Top-down движение с WASD
- Сетевая синхронизация позиции
- Система здоровья
- Интерполяция для плавности движения

## Масштабирование

Архитектура готова для расширения:

1. **Зоны и инстансы** - GameWorld можно разделить на зоны
2. **База данных** - Добавить сохранение персонажей
3. **Инвентарь и предметы** - Система предметов
4. **Квесты и NPC** - Игровой контент
5. **Чат и гильдии** - Социальные функции

## Требования

- Godot 4.4+
- Сетевое подключение для мультиплеера

## Разработка

Проект использует современные практики Godot 4.4:
- ENetMultiplayerPeer для сети
- MultiplayerSynchronizer для репликации
- Autoload для глобальных менеджеров
- Сигналы для связи между компонентами

## Лицензия

Проект создан для демонстрации возможностей создания MMORPG на Godot 4.4.
