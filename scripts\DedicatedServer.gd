extends Node

# DedicatedServer - Скрипт для запуска dedicated сервера
# Автоматически создает сервер при запуске в headless режиме

const DEFAULT_PORT = 7000
const MAX_PLAYERS = 100

func _ready():
	# Проверяем аргументы командной строки для запуска dedicated server
	var args = OS.get_cmdline_args()
	var should_start_server = false

	# Проверяем, запущен ли проект в headless режиме
	if DisplayServer.get_name() == "headless":
		should_start_server = true
		print("Detected headless mode")

	# Проверяем аргумент --server
	for arg in args:
		if arg == "--server" or arg == "--dedicated-server":
			should_start_server = true
			break

	if should_start_server:
		print("Starting dedicated server...")
		start_dedicated_server()
	else:
		print("Running in client mode")

func start_dedicated_server():
	# Устанавливаем настройки для сервера
	var port = DEFAULT_PORT
	
	# Проверяем аргументы командной строки
	var args = OS.get_cmdline_args()
	for i in range(args.size()):
		if args[i] == "--port" and i + 1 < args.size():
			port = int(args[i + 1])
		elif args[i] == "--max-players" and i + 1 < args.size():
			NetworkManager.MAX_PLAYERS = int(args[i + 1])
	
	print("Server configuration:")
	print("  Port: ", port)
	print("  Max players: ", NetworkManager.MAX_PLAYERS)
	
	# Создаем сервер
	if NetworkManager.create_server(port):
		print("Dedicated server started successfully on port ", port)
		
		# Загружаем игровой мир
		GameManager.load_game_world()
		
		# Настраиваем автосохранение и другие серверные функции
		setup_server_features()
	else:
		print("Failed to start dedicated server")
		get_tree().quit(1)

func setup_server_features():
	# Настраиваем периодическое сохранение состояния
	var save_timer = Timer.new()
	save_timer.wait_time = 300.0  # Сохранение каждые 5 минут
	save_timer.timeout.connect(save_server_state)
	save_timer.autostart = true
	add_child(save_timer)
	
	# Настраиваем мониторинг сервера
	var monitor_timer = Timer.new()
	monitor_timer.wait_time = 60.0  # Мониторинг каждую минуту
	monitor_timer.timeout.connect(print_server_stats)
	monitor_timer.autostart = true
	add_child(monitor_timer)
	
	print("Server features initialized")

func save_server_state():
	# Здесь можно добавить логику сохранения состояния игры
	print("Saving server state... (", NetworkManager.get_player_count(), " players online)")

func print_server_stats():
	var player_count = NetworkManager.get_player_count()
	var memory_usage = OS.get_static_memory_usage_by_type()
	
	print("=== Server Stats ===")
	print("Players online: ", player_count, "/", NetworkManager.MAX_PLAYERS)
	print("Memory usage: ", memory_usage.get("Object", 0) / 1024 / 1024, " MB")
	print("Uptime: ", Time.get_ticks_msec() / 1000 / 60, " minutes")
	print("===================")
