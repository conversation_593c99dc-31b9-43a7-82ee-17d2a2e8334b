extends Node2D

# GameWorld - Основной игровой мир
# Управляет игровой средой, спавном объектов и игровой логикой

@onready var players_container: Node2D = $PlayersContainer
@onready var camera: Camera2D = $Camera2D
@onready var ui_layer: CanvasLayer = $UILayer
@onready var hud: Control = $UILayer/HUD
@onready var menu_button: Button = $UILayer/HUD/BottomPanel/MenuButton
@onready var player_name_label: Label = $UILayer/HUD/TopPanel/PlayerInfo/PlayerNameLabel
@onready var health_bar: ProgressBar = $UILayer/HUD/TopPanel/PlayerInfo/HealthBar
@onready var players_count_label: Label = $UILayer/HUD/TopPanel/NetworkInfo/PlayersCountLabel

var world_size: Vector2 = Vector2(2000, 2000)
var local_player: Node2D

func _ready():
	print("GameWorld initialized")

	# Подключаемся к сигналам GameManager
	GameManager.player_spawned.connect(_on_player_spawned)
	GameManager.player_despawned.connect(_on_player_despawned)

	# Подключаем кнопку меню
	if menu_button:
		menu_button.pressed.connect(_on_menu_button_pressed)

	# Настраиваем камеру
	setup_camera()

	# Создаем базовую среду
	create_environment()

func setup_camera():
	# Устанавливаем лимиты камеры
	camera.limit_left = -world_size.x / 2
	camera.limit_right = world_size.x / 2
	camera.limit_top = -world_size.y / 2
	camera.limit_bottom = world_size.y / 2
	
	# Включаем сглаживание
	camera.enabled = true

func create_environment():
	# Создаем простую среду для тестирования
	var environment = Node2D.new()
	environment.name = "Environment"
	add_child(environment)
	
	# Добавляем несколько препятствий
	create_obstacles(environment)
	
	# Добавляем границы мира
	create_world_boundaries(environment)

func create_obstacles(parent: Node2D):
	# Создаем несколько простых препятствий
	var obstacles_data = [
		{"pos": Vector2(200, 200), "size": Vector2(100, 50)},
		{"pos": Vector2(-300, 150), "size": Vector2(80, 80)},
		{"pos": Vector2(100, -250), "size": Vector2(120, 60)},
		{"pos": Vector2(-200, -200), "size": Vector2(90, 90)},
		{"pos": Vector2(400, -100), "size": Vector2(60, 100)}
	]
	
	for obstacle_data in obstacles_data:
		var obstacle = create_obstacle(obstacle_data.pos, obstacle_data.size)
		parent.add_child(obstacle)

func create_obstacle(position: Vector2, size: Vector2) -> StaticBody2D:
	var obstacle = StaticBody2D.new()
	obstacle.position = position
	
	# Создаем коллизию
	var collision = CollisionShape2D.new()
	var shape = RectangleShape2D.new()
	shape.size = size
	collision.shape = shape
	obstacle.add_child(collision)
	
	# Создаем визуал
	var sprite = ColorRect.new()
	sprite.size = size
	sprite.position = -size / 2
	sprite.color = Color(0.4, 0.3, 0.2, 1.0)  # Коричневый цвет
	obstacle.add_child(sprite)
	
	return obstacle

func create_world_boundaries(parent: Node2D):
	var boundaries = StaticBody2D.new()
	boundaries.name = "WorldBoundaries"
	parent.add_child(boundaries)
	
	var thickness = 50
	var half_world = world_size / 2
	
	# Создаем границы мира
	var boundary_data = [
		{"pos": Vector2(0, -half_world.y - thickness/2), "size": Vector2(world_size.x + thickness*2, thickness)},  # Верх
		{"pos": Vector2(0, half_world.y + thickness/2), "size": Vector2(world_size.x + thickness*2, thickness)},   # Низ
		{"pos": Vector2(-half_world.x - thickness/2, 0), "size": Vector2(thickness, world_size.y)},                # Лево
		{"pos": Vector2(half_world.x + thickness/2, 0), "size": Vector2(thickness, world_size.y)}                  # Право
	]
	
	for boundary in boundary_data:
		var collision = CollisionShape2D.new()
		var shape = RectangleShape2D.new()
		shape.size = boundary.size
		collision.shape = shape
		collision.position = boundary.pos
		boundaries.add_child(collision)

func _on_player_spawned(player_id: int):
	var player = GameManager.get_player(player_id)
	if player:
		# Перемещаем игрока в контейнер игроков
		if player.get_parent():
			player.get_parent().remove_child(player)
		players_container.add_child(player)
		
		# Если это локальный игрок, настраиваем камеру
		if player_id == NetworkManager.get_local_player_id():
			local_player = player
			setup_local_player_camera()

func _on_player_despawned(player_id: int):
	if local_player and local_player.name == "Player_" + str(player_id):
		local_player = null

func setup_local_player_camera():
	if local_player:
		# Привязываем камеру к локальному игроку
		camera.global_position = local_player.global_position

func _process(_delta):
	# Обновляем позицию камеры для следования за локальным игроком
	if local_player and is_instance_valid(local_player):
		camera.global_position = local_player.global_position

	# Обновляем HUD
	update_hud()

func update_hud():
	# Обновляем информацию об игроке
	if local_player and is_instance_valid(local_player):
		if player_name_label:
			player_name_label.text = "Игрок: " + local_player.player_name
		if health_bar:
			health_bar.value = local_player.current_health

	# Обновляем количество игроков
	if players_count_label:
		players_count_label.text = "Игроков: " + str(GameManager.get_player_count())

# Получение случайной позиции спавна в мире
func get_random_spawn_position() -> Vector2:
	var margin = 100
	var x = randf_range(-world_size.x/2 + margin, world_size.x/2 - margin)
	var y = randf_range(-world_size.y/2 + margin, world_size.y/2 - margin)
	return Vector2(x, y)

# Проверка, находится ли позиция в границах мира
func is_position_in_world(position: Vector2) -> bool:
	var half_world = world_size / 2
	return position.x >= -half_world.x and position.x <= half_world.x and \
		   position.y >= -half_world.y and position.y <= half_world.y

# Обработка кнопки меню
func _on_menu_button_pressed():
	GameManager.quit_to_menu()

# Обработка клавиш
func _input(event):
	if event is InputEventKey and event.pressed:
		if event.keycode == KEY_ESCAPE:
			GameManager.quit_to_menu()
