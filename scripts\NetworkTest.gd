extends Node

# NetworkTest - Простой скрипт для тестирования сетевых функций
# Можно использовать для отладки и проверки работы сети

func _ready():
	print("NetworkTest initialized")
	
	# Подключаемся к сигналам для тестирования
	NetworkManager.player_connected.connect(_on_test_player_connected)
	NetworkManager.player_disconnected.connect(_on_test_player_disconnected)
	NetworkManager.connection_succeeded.connect(_on_test_connection_succeeded)
	NetworkManager.connection_failed.connect(_on_test_connection_failed)

func _on_test_player_connected(player_id: int):
	print("TEST: Player connected - ", player_id)
	
	# Тестируем отправку данных
	test_rpc_functions(player_id)

func _on_test_player_disconnected(player_id: int):
	print("TEST: Player disconnected - ", player_id)

func _on_test_connection_succeeded():
	print("TEST: Connection successful")

func _on_test_connection_failed():
	print("TEST: Connection failed")

func test_rpc_functions(player_id: int):
	# Тестируем различные RPC функции
	if NetworkManager.is_server:
		print("TEST: Testing RPC functions for player ", player_id)
		
		# Тест синхронизации позиции
		NetworkManager.update_player_position.rpc_id(player_id, Vector2(100, 100))
		
		# Тест синхронизации здоровья
		NetworkManager.update_player_health.rpc_id(player_id, 95.0)

# Функция для создания тестового сервера
func create_test_server(port: int = 7001):
	print("Creating test server on port ", port)
	return NetworkManager.create_server(port)

# Функция для подключения к тестовому серверу
func connect_to_test_server(address: String = "127.0.0.1", port: int = 7001):
	print("Connecting to test server at ", address, ":", port)
	return NetworkManager.connect_to_server(address, port)

# Функция для симуляции множественных подключений (только для тестирования)
func simulate_multiple_connections(count: int = 5):
	if not NetworkManager.is_server:
		print("ERROR: Can only simulate connections on server")
		return
	
	print("Simulating ", count, " connections...")
	
	for i in range(count):
		var fake_id = 1000 + i
		var fake_player = NetworkManager.PlayerData.new(fake_id, "TestPlayer_" + str(i))
		fake_player.position = Vector2(randf_range(-500, 500), randf_range(-500, 500))
		NetworkManager.connected_players[fake_id] = fake_player
		
		# Эмулируем сигнал подключения
		NetworkManager.player_connected.emit(fake_id)
		
		await get_tree().create_timer(0.1).timeout

# Функция для вывода статистики сети
func print_network_stats():
	print("=== Network Stats ===")
	print("Is Server: ", NetworkManager.is_server)
	print("Is Dedicated: ", NetworkManager.is_dedicated_server)
	print("Player Count: ", NetworkManager.get_player_count())
	print("Local Player ID: ", NetworkManager.get_local_player_id())
	
	if NetworkManager.multiplayer_peer:
		print("Peer State: ", NetworkManager.multiplayer_peer.get_connection_status())
	
	print("Connected Players:")
	for player_data in NetworkManager.get_all_players():
		print("  - ", player_data.name, " (ID: ", player_data.id, ", Pos: ", player_data.position, ")")
	
	print("====================")

# Консольные команды для тестирования
func _input(event):
	if event is InputEventKey and event.pressed:
		match event.keycode:
			KEY_F1:
				print_network_stats()
			KEY_F2:
				if NetworkManager.is_server:
					simulate_multiple_connections(3)
			KEY_F3:
				create_test_server()
			KEY_F4:
				connect_to_test_server()
			KEY_F5:
				NetworkManager.disconnect_from_network()

func _notification(what):
	if what == NOTIFICATION_WM_CLOSE_REQUEST:
		# Корректно отключаемся при закрытии
		NetworkManager.disconnect_from_network()
		get_tree().quit()
