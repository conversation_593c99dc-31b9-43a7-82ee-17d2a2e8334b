[gd_scene load_steps=3 format=3 uid="uid://bqxvhqjxqxqxr"]

[ext_resource type="Script" path="res://scripts/GameWorld.gd" id="1_1a2b4"]
[ext_resource type="PackedScene" uid="uid://bqxvhqjxqxqxt" path="res://scenes/DebugConsole.tscn" id="2_debug"]

[node name="GameWorld" type="Node2D"]
script = ExtResource("1_1a2b4")

[node name="PlayersContainer" type="Node2D" parent="."]

[node name="Camera2D" type="Camera2D" parent="."]
zoom = Vector2(1.5, 1.5)

[node name="UILayer" type="CanvasLayer" parent="."]

[node name="HUD" type="Control" parent="UILayer"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0

[node name="TopPanel" type="Panel" parent="UILayer/HUD"]
layout_mode = 1
anchors_preset = 10
anchor_right = 1.0
offset_bottom = 50.0
modulate = Color(1, 1, 1, 0.8)

[node name="PlayerInfo" type="HBoxContainer" parent="UILayer/HUD/TopPanel"]
layout_mode = 1
anchors_preset = 4
anchor_top = 0.5
anchor_bottom = 0.5
offset_left = 10.0
offset_top = -12.0
offset_right = 200.0
offset_bottom = 12.0

[node name="PlayerNameLabel" type="Label" parent="UILayer/HUD/TopPanel/PlayerInfo"]
layout_mode = 2
text = "Игрок: "

[node name="HealthBar" type="ProgressBar" parent="UILayer/HUD/TopPanel/PlayerInfo"]
layout_mode = 2
size_flags_horizontal = 3
max_value = 100.0
value = 100.0
show_percentage = false

[node name="NetworkInfo" type="VBoxContainer" parent="UILayer/HUD/TopPanel"]
layout_mode = 1
anchors_preset = 6
anchor_left = 1.0
anchor_top = 0.5
anchor_right = 1.0
anchor_bottom = 0.5
offset_left = -150.0
offset_top = -20.0
offset_right = -10.0
offset_bottom = 20.0

[node name="PlayersCountLabel" type="Label" parent="UILayer/HUD/TopPanel/NetworkInfo"]
layout_mode = 2
text = "Игроков: 0"
horizontal_alignment = 2

[node name="PingLabel" type="Label" parent="UILayer/HUD/TopPanel/NetworkInfo"]
layout_mode = 2
text = "Пинг: 0ms"
horizontal_alignment = 2

[node name="BottomPanel" type="Panel" parent="UILayer/HUD"]
layout_mode = 1
anchors_preset = 12
anchor_top = 1.0
anchor_right = 1.0
anchor_bottom = 1.0
offset_top = -80.0
modulate = Color(1, 1, 1, 0.8)

[node name="ControlsInfo" type="VBoxContainer" parent="UILayer/HUD/BottomPanel"]
layout_mode = 1
anchors_preset = 4
anchor_top = 0.5
anchor_bottom = 0.5
offset_left = 10.0
offset_top = -30.0
offset_right = 300.0
offset_bottom = 30.0

[node name="MovementLabel" type="Label" parent="UILayer/HUD/BottomPanel/ControlsInfo"]
layout_mode = 2
text = "WASD - движение"

[node name="EscapeLabel" type="Label" parent="UILayer/HUD/BottomPanel/ControlsInfo"]
layout_mode = 2
text = "ESC - выход в меню"

[node name="MenuButton" type="Button" parent="UILayer/HUD/BottomPanel"]
layout_mode = 1
anchors_preset = 6
anchor_left = 1.0
anchor_top = 0.5
anchor_right = 1.0
anchor_bottom = 0.5
offset_left = -100.0
offset_top = -15.0
offset_right = -10.0
offset_bottom = 15.0
text = "Меню"

[node name="DebugConsole" parent="UILayer" instance=ExtResource("2_debug")]
