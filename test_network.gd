extends SceneTree

# Простой тест для проверки сетевых функций
# Запуск: godot --script test_network.gd

func _init():
	print("Starting network test...")
	
	# Тест 1: Создание сервера
	test_server_creation()
	
	# Ждем немного
	await create_timer(1.0).timeout
	
	# Тест 2: Подключение клиента
	test_client_connection()
	
	# Ждем завершения тестов
	await create_timer(3.0).timeout
	
	print("Network test completed")
	quit()

func test_server_creation():
	print("Test 1: Creating server...")
	
	# Создаем NetworkManager
	var network_manager = preload("res://scripts/NetworkManager.gd").new()
	network_manager.name = "NetworkManager"
	root.add_child(network_manager)
	
	# Пытаемся создать сервер
	var result = network_manager.create_server(7002)
	if result:
		print("✓ Server created successfully")
	else:
		print("✗ Failed to create server")

func test_client_connection():
	print("Test 2: Testing client connection...")
	
	# Создаем второй NetworkManager для клиента
	var client_manager = preload("res://scripts/NetworkManager.gd").new()
	client_manager.name = "ClientManager"
	root.add_child(client_manager)
	
	# Пытаемся подключиться
	var result = client_manager.connect_to_server("127.0.0.1", 7002)
	if result:
		print("✓ Client connection initiated")
	else:
		print("✗ Failed to initiate client connection")
