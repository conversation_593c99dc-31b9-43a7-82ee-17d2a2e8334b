extends Control

# MainMenu - Главное меню игры с возможностью подключения к серверу или создания сервера

@onready var player_name_input: LineEdit = $VBoxContainer/PlayerNameContainer/PlayerNameInput
@onready var server_address_input: LineEdit = $VBoxContainer/ConnectionContainer/ServerAddressInput
@onready var server_port_input: SpinBox = $VBoxContainer/ConnectionContainer/ServerPortInput
@onready var connect_button: Button = $VBoxContainer/ConnectionContainer/ConnectButton
@onready var create_server_button: Button = $VBoxContainer/ServerContainer/CreateServerButton
@onready var server_port_input_server: SpinBox = $VBoxContainer/ServerContainer/ServerPortInput
@onready var status_label: Label = $VBoxContainer/StatusLabel
@onready var disconnect_button: Button = $VBoxContainer/DisconnectButton

var is_connecting: bool = false

func _ready():
	add_to_group("main_menu")
	
	# Подключаем сигналы кнопок
	connect_button.pressed.connect(_on_connect_button_pressed)
	create_server_button.pressed.connect(_on_create_server_button_pressed)
	if disconnect_button:
		disconnect_button.pressed.connect(_on_disconnect_button_pressed)
	
	# Подключаем сигналы NetworkManager
	NetworkManager.connection_succeeded.connect(_on_connection_succeeded)
	NetworkManager.connection_failed.connect(_on_connection_failed)
	NetworkManager.server_started.connect(_on_server_started)
	
	# Устанавливаем значения по умолчанию
	player_name_input.text = "Stalker_" + str(randi() % 1000)
	server_address_input.text = "127.0.0.1"
	server_port_input.value = 7000
	server_port_input_server.value = 7000
	
	update_status("Готов к подключению")
	update_ui_state()

func _on_connect_button_pressed():
	if is_connecting:
		return

	# Проверяем, не запущен ли уже сервер
	if NetworkManager.is_server:
		update_status("Сервер уже запущен! Нельзя подключиться к другому серверу.")
		return

	var player_name = player_name_input.text.strip_edges()
	var server_address = server_address_input.text.strip_edges()
	var port = int(server_port_input.value)

	if player_name == "":
		update_status("Введите имя игрока!")
		return

	if server_address == "":
		update_status("Введите адрес сервера!")
		return

	is_connecting = true
	connect_button.disabled = true
	create_server_button.disabled = true

	GameManager.player_name = player_name
	update_status("Подключение к серверу...")

	if not NetworkManager.connect_to_server(server_address, port):
		_on_connection_failed()

func _on_create_server_button_pressed():
	if is_connecting:
		return

	# Проверяем, не подключены ли мы уже к серверу
	if NetworkManager.multiplayer_peer and NetworkManager.multiplayer_peer.get_connection_status() != MultiplayerPeer.CONNECTION_DISCONNECTED:
		update_status("Уже подключен к сети! Отключитесь сначала.")
		return

	var player_name = player_name_input.text.strip_edges()
	var port = int(server_port_input_server.value)

	if player_name == "":
		update_status("Введите имя игрока!")
		return

	is_connecting = true
	connect_button.disabled = true
	create_server_button.disabled = true

	GameManager.player_name = player_name
	update_status("Создание сервера...")

	if not NetworkManager.create_server(port):
		update_status("Ошибка создания сервера!")
		reset_buttons()

func _on_connection_succeeded():
	update_status("Подключение успешно! Загрузка игры...")

func _on_connection_failed():
	update_status("Ошибка подключения к серверу!")
	reset_buttons()

func _on_server_started():
	update_status("Сервер создан! Загрузка игры...")

func _on_disconnect_button_pressed():
	NetworkManager.disconnect_from_network()
	update_status("Отключен от сети")
	reset_buttons()
	update_ui_state()

func update_status(message: String):
	status_label.text = message
	print("MainMenu: ", message)

func reset_buttons():
	is_connecting = false
	connect_button.disabled = false
	create_server_button.disabled = false

func update_ui_state():
	# Обновляем состояние кнопок в зависимости от сетевого состояния
	var is_connected = NetworkManager.multiplayer_peer and NetworkManager.multiplayer_peer.get_connection_status() != MultiplayerPeer.CONNECTION_DISCONNECTED

	if disconnect_button:
		disconnect_button.visible = is_connected

	if is_connected:
		if NetworkManager.is_server:
			update_status("Сервер запущен (порт: " + str(NetworkManager.DEFAULT_PORT) + ")")
		else:
			update_status("Подключен к серверу")
		connect_button.disabled = true
		create_server_button.disabled = true
	else:
		if not is_connecting:
			connect_button.disabled = false
			create_server_button.disabled = false

func _input(event):
	if event is InputEventKey and event.pressed:
		if event.keycode == KEY_ENTER:
			if server_address_input.has_focus() or server_port_input.has_focus() or player_name_input.has_focus():
				_on_connect_button_pressed()
		elif event.keycode == KEY_ESCAPE:
			get_tree().quit()
