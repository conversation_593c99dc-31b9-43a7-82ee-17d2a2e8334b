[gd_scene load_steps=3 format=3 uid="uid://bqxvhqjxqxqxs"]

[ext_resource type="Script" path="res://scripts/Player.gd" id="1_1a2b5"]

[sub_resource type="RectangleShape2D" id="RectangleShape2D_1a2b7"]
size = Vector2(32, 32)

[node name="Player" type="CharacterBody2D"]
script = ExtResource("1_1a2b5")

[node name="Sprite" type="ColorRect" parent="."]
offset_left = -16.0
offset_top = -16.0
offset_right = 16.0
offset_bottom = 16.0
color = Color(0, 1, 0, 1)

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
shape = SubResource("RectangleShape2D_1a2b7")

[node name="NameLabel" type="Label" parent="."]
offset_left = -50.0
offset_top = -40.0
offset_right = 50.0
offset_bottom = -20.0
text = "Player"
horizontal_alignment = 1

[node name="HealthBar" type="ProgressBar" parent="."]
offset_left = -25.0
offset_top = 25.0
offset_right = 25.0
offset_bottom = 33.0
max_value = 100.0
value = 100.0
show_percentage = false

[sub_resource type="SceneReplicationConfig" id="SceneReplicationConfig_1a2b6"]
properties/0/path = NodePath(".:position")
properties/0/spawn = true
properties/0/replication_mode = 1
properties/1/path = NodePath(".:velocity")
properties/1/spawn = true
properties/1/replication_mode = 1

[node name="MultiplayerSynchronizer" type="MultiplayerSynchronizer" parent="."]
replication_config = SubResource("SceneReplicationConfig_1a2b6")
