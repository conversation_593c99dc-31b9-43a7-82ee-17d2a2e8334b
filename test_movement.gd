extends SceneTree

# Тест движения - проверяет всю систему движения

func _init():
	print("=== Movement Test ===")
	
	# Тест 1: Проверка input map
	test_input_map()
	
	# Тест 2: Создание сервера и игрока
	test_player_creation()
	
	await create_timer(2.0).timeout
	print("=== Test completed ===")
	quit()

func test_input_map():
	print("\n--- Testing Input Map ---")
	
	var actions = ["move_up", "move_down", "move_left", "move_right"]
	
	for action in actions:
		if InputMap.has_action(action):
			print("✓ Action '", action, "' exists")
			var events = InputMap.action_get_events(action)
			for event in events:
				if event is InputEventKey:
					print("  - Key: ", event.physical_keycode, " (", char(event.physical_keycode), ")")
		else:
			print("✗ Action '", action, "' NOT found")

func test_player_creation():
	print("\n--- Testing Player Creation ---")
	
	# Создаем NetworkManager
	var network_manager = preload("res://scripts/NetworkManager.gd").new()
	network_manager.name = "NetworkManager"
	root.add_child(network_manager)
	
	# Создаем GameManager
	var game_manager = preload("res://scripts/GameManager.gd").new()
	game_manager.name = "GameManager"
	root.add_child(game_manager)
	
	# Создаем сервер
	if network_manager.create_server(7004):
		print("✓ Server created")
		
		# Ждем немного
		await create_timer(0.5).timeout
		
		# Загружаем игровой мир
		game_manager.load_game_world()
		
		# Ждем еще немного
		await create_timer(0.5).timeout
		
		# Проверяем, есть ли игроки
		var player_count = game_manager.get_player_count()
		print("Player count: ", player_count)
		
		if player_count > 0:
			print("✓ Player spawned")
			var local_player = game_manager.get_local_player()
			if local_player:
				print("✓ Local player found: ", local_player.player_name)
				print("  - ID: ", local_player.player_id)
				print("  - Is Local: ", local_player.is_local_player)
				print("  - Authority: ", local_player.get_multiplayer_authority())
			else:
				print("✗ Local player not found")
		else:
			print("✗ No players spawned")
	else:
		print("✗ Failed to create server")
