@echo off
echo Starting STALZONE ONLINE Client...
echo.

REM Попробуем найти Godot в разных местах
set GODOT_PATH=""

REM Проверяем стандартные пути установки Godot
if exist "C:\Program Files\Godot\godot.exe" set GODOT_PATH="C:\Program Files\Godot\godot.exe"
if exist "C:\Program Files (x86)\Godot\godot.exe" set GODOT_PATH="C:\Program Files (x86)\Godot\godot.exe"
if exist "C:\Godot\godot.exe" set GODOT_PATH="C:\Godot\godot.exe"
if exist "godot.exe" set GODOT_PATH="godot.exe"

REM Если Godot найден, запускаем клиент
if not %GODOT_PATH%=="" (
    echo Found Godot at %GODOT_PATH%
    %GODOT_PATH%
) else (
    echo ERROR: Godot not found!
    echo Please either:
    echo 1. Add <PERSON>ot to your PATH environment variable
    echo 2. Copy godot.exe to this folder
    echo 3. Edit this bat file with correct Godot path
    echo 4. Or just open the project in Godot Editor
)

pause
