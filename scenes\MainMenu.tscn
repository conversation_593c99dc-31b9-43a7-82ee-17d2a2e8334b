[gd_scene load_steps=2 format=3 uid="uid://bqxvhqjxqxqxq"]

[ext_resource type="Script" path="res://scripts/MainMenu.gd" id="1_1a2b3"]

[node name="MainMenu" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
script = ExtResource("1_1a2b3")

[node name="Background" type="ColorRect" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
color = Color(0.1, 0.1, 0.1, 1)

[node name="VBoxContainer" type="VBoxContainer" parent="."]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -200.0
offset_top = -200.0
offset_right = 200.0
offset_bottom = 200.0

[node name="TitleLabel" type="Label" parent="VBoxContainer"]
layout_mode = 2
text = "STALZONE ONLINE"
horizontal_alignment = 1
vertical_alignment = 1

[node name="HSeparator" type="HSeparator" parent="VBoxContainer"]
layout_mode = 2

[node name="PlayerNameContainer" type="VBoxContainer" parent="VBoxContainer"]
layout_mode = 2

[node name="PlayerNameLabel" type="Label" parent="VBoxContainer/PlayerNameContainer"]
layout_mode = 2
text = "Имя игрока:"

[node name="PlayerNameInput" type="LineEdit" parent="VBoxContainer/PlayerNameContainer"]
layout_mode = 2
placeholder_text = "Введите ваше имя"

[node name="HSeparator2" type="HSeparator" parent="VBoxContainer"]
layout_mode = 2

[node name="ConnectionContainer" type="VBoxContainer" parent="VBoxContainer"]
layout_mode = 2

[node name="ConnectionLabel" type="Label" parent="VBoxContainer/ConnectionContainer"]
layout_mode = 2
text = "Подключение к серверу:"
horizontal_alignment = 1

[node name="ServerAddressLabel" type="Label" parent="VBoxContainer/ConnectionContainer"]
layout_mode = 2
text = "Адрес сервера:"

[node name="ServerAddressInput" type="LineEdit" parent="VBoxContainer/ConnectionContainer"]
layout_mode = 2
placeholder_text = "127.0.0.1"

[node name="ServerPortLabel" type="Label" parent="VBoxContainer/ConnectionContainer"]
layout_mode = 2
text = "Порт:"

[node name="ServerPortInput" type="SpinBox" parent="VBoxContainer/ConnectionContainer"]
layout_mode = 2
min_value = 1024.0
max_value = 65535.0
value = 7000.0

[node name="ConnectButton" type="Button" parent="VBoxContainer/ConnectionContainer"]
layout_mode = 2
text = "Подключиться"

[node name="HSeparator3" type="HSeparator" parent="VBoxContainer"]
layout_mode = 2

[node name="ServerContainer" type="VBoxContainer" parent="VBoxContainer"]
layout_mode = 2

[node name="ServerLabel" type="Label" parent="VBoxContainer/ServerContainer"]
layout_mode = 2
text = "Создать сервер:"
horizontal_alignment = 1

[node name="ServerPortLabel" type="Label" parent="VBoxContainer/ServerContainer"]
layout_mode = 2
text = "Порт сервера:"

[node name="ServerPortInput" type="SpinBox" parent="VBoxContainer/ServerContainer"]
layout_mode = 2
min_value = 1024.0
max_value = 65535.0
value = 7000.0

[node name="CreateServerButton" type="Button" parent="VBoxContainer/ServerContainer"]
layout_mode = 2
text = "Создать сервер"

[node name="HSeparator4" type="HSeparator" parent="VBoxContainer"]
layout_mode = 2

[node name="StatusLabel" type="Label" parent="VBoxContainer"]
layout_mode = 2
text = "Готов к подключению"
horizontal_alignment = 1
vertical_alignment = 1
