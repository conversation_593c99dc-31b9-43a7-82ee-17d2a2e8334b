@echo off
echo Checking Godot installation...
echo.

REM Проверяем Godot в PATH
godot --version >nul 2>&1
if %errorlevel%==0 (
    echo ✓ Godot found in PATH
    godot --version
    echo.
    echo You can run the game with:
    echo   start_client.bat  - for client
    echo   start_server.bat  - for server
    goto :end
)

REM Проверяем локальный godot.exe
if exist "godot.exe" (
    echo ✓ Godot found locally
    godot.exe --version
    echo.
    echo You can run the game with:
    echo   start_client.bat  - for client
    echo   start_server.bat  - for server
    goto :end
)

REM Проверяем стандартные пути
set FOUND=0

if exist "C:\Program Files\Godot\godot.exe" (
    echo ✓ Godot found at: C:\Program Files\Godot\godot.exe
    set FOUND=1
)

if exist "C:\Program Files (x86)\Godot\godot.exe" (
    echo ✓ Godot found at: C:\Program Files (x86)\Godot\godot.exe
    set FOUND=1
)

if exist "C:\Godot\godot.exe" (
    echo ✓ Godot found at: C:\Godot\godot.exe
    set FOUND=1
)

if %FOUND%==1 (
    echo.
    echo Please either:
    echo 1. Add Godot to your PATH environment variable
    echo 2. Copy godot.exe to this project folder
    echo 3. Open the project directly in Godot Editor
    goto :end
)

echo ✗ Godot NOT found!
echo.
echo Please install Godot 4.4+ from: https://godotengine.org/download
echo Then either:
echo 1. Add Godot to your PATH environment variable
echo 2. Copy godot.exe to this project folder
echo 3. Open the project directly in Godot Editor

:end
echo.
pause
