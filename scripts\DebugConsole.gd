extends Control

# DebugConsole - Консоль для отладки и тестирования

@onready var output_label: RichTextLabel = $VBoxContainer/OutputLabel
@onready var input_line: LineEdit = $VBoxContainer/InputLine

var max_lines: int = 50
var current_lines: int = 0

func _ready():
	# Подключаем сигналы
	input_line.text_submitted.connect(_on_input_submitted)
	
	# Подключаемся к сигналам для логирования
	NetworkManager.player_connected.connect(_on_debug_player_connected)
	NetworkManager.player_disconnected.connect(_on_debug_player_disconnected)
	NetworkManager.server_started.connect(_on_debug_server_started)
	NetworkManager.connection_succeeded.connect(_on_debug_connection_succeeded)
	
	add_line("Debug Console initialized")
	add_line("Commands: help, status, players, spawn_test, clear")

func _on_input_submitted(text: String):
	var command = text.strip_edges().to_lower()
	input_line.clear()
	
	add_line("> " + command)
	
	match command:
		"help":
			show_help()
		"status":
			show_status()
		"players":
			show_players()
		"spawn_test":
			spawn_test_player()
		"clear":
			clear_output()
		"server":
			create_test_server()
		"connect":
			connect_test_client()
		_:
			add_line("Unknown command: " + command + ". Type 'help' for commands.")

func show_help():
	add_line("Available commands:")
	add_line("  help - Show this help")
	add_line("  status - Show network status")
	add_line("  players - Show connected players")
	add_line("  spawn_test - Spawn test player")
	add_line("  server - Create test server")
	add_line("  connect - Connect to localhost")
	add_line("  clear - Clear console")

func show_status():
	add_line("=== Network Status ===")
	add_line("Is Server: " + str(NetworkManager.is_server))
	add_line("Is Dedicated: " + str(NetworkManager.is_dedicated_server))
	add_line("Player Count: " + str(NetworkManager.get_player_count()))
	add_line("Local Player ID: " + str(NetworkManager.get_local_player_id()))
	add_line("Game State: " + str(GameManager.current_state))
	
	if NetworkManager.multiplayer_peer:
		add_line("Peer Status: " + str(NetworkManager.multiplayer_peer.get_connection_status()))
	else:
		add_line("No multiplayer peer")

func show_players():
	add_line("=== Connected Players ===")
	var players = NetworkManager.get_all_players()
	if players.size() == 0:
		add_line("No players connected")
	else:
		for player_data in players:
			add_line("Player: " + player_data.name + " (ID: " + str(player_data.id) + ")")
			add_line("  Position: " + str(player_data.position))
			add_line("  Health: " + str(player_data.health))

func spawn_test_player():
	if NetworkManager.is_server:
		var test_id = 999
		var test_name = "TestPlayer"
		var test_pos = Vector2(randf_range(-200, 200), randf_range(-200, 200))
		
		if GameManager.current_state == GameManager.GameState.IN_GAME:
			GameManager.spawn_player(test_id, test_name, test_pos)
			add_line("Spawned test player at " + str(test_pos))
		else:
			add_line("Not in game state")
	else:
		add_line("Only server can spawn test players")

func create_test_server():
	add_line("Creating test server...")
	if NetworkManager.create_server(7001):
		add_line("Test server created on port 7001")
	else:
		add_line("Failed to create test server")

func connect_test_client():
	add_line("Connecting to localhost:7001...")
	if NetworkManager.connect_to_server("127.0.0.1", 7001):
		add_line("Attempting connection...")
	else:
		add_line("Failed to start connection")

func clear_output():
	output_label.clear()
	current_lines = 0
	add_line("Console cleared")

func add_line(text: String):
	if current_lines >= max_lines:
		# Удаляем старые строки
		var lines = output_label.get_parsed_text().split("\n")
		output_label.clear()
		current_lines = 0
		
		# Добавляем последние строки
		for i in range(max(0, lines.size() - max_lines + 10), lines.size()):
			if i < lines.size():
				output_label.append_text(lines[i] + "\n")
				current_lines += 1
	
	output_label.append_text(text + "\n")
	current_lines += 1
	
	# Прокручиваем вниз
	await get_tree().process_frame
	output_label.scroll_to_line(current_lines)

# Обработчики событий для логирования
func _on_debug_player_connected(player_id: int):
	add_line("EVENT: Player connected - " + str(player_id))

func _on_debug_player_disconnected(player_id: int):
	add_line("EVENT: Player disconnected - " + str(player_id))

func _on_debug_server_started():
	add_line("EVENT: Server started")

func _on_debug_connection_succeeded():
	add_line("EVENT: Connection succeeded")

func _input(event):
	if event is InputEventKey and event.pressed:
		if event.keycode == KEY_F12:
			visible = !visible
			if visible:
				input_line.grab_focus()
