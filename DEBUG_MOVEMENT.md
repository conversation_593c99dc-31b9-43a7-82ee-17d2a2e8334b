# Отладка движения WASD

## Проблема
Игрок не может двигаться на WASD после подключения к серверу.

## Возможные причины и решения

### 1. Проверка Input Map
Запустите тест:
```bash
godot --script test_movement.gd
```

### 2. Проверка в игре
1. Запустите игру в Godot Editor
2. Создайте сервер
3. Нажмите F1 в игре для проверки input actions
4. Нажмите F12 для консоли отладки
5. Используйте команду `status` для проверки состояния

### 3. Отладочная информация
В консоли должны появляться сообщения:
- "Player setup complete: [имя] (ID: [id], Local: true/false)"
- "Input detected: [вектор] for player [имя]" (при движении)

### 4. Проверка локального игрока
В консоли отладки (F12) выполните:
- `status` - покажет ID локального игрока
- `players` - покажет всех игроков

### 5. Ручная проверка
Если движение не работает, проверьте:
1. Зеленый ли квадрат (локальный игрок)?
2. Показывает ли консоль "Local: true" для вашего игрока?
3. Работают ли клавиши WASD в других частях игры?

## Ожидаемое поведение
- Зеленый квадрат = локальный игрок (должен двигаться)
- Синий квадрат = удаленный игрок (не должен двигаться от вашего ввода)
- При нажатии WASD должны появляться сообщения в консоли

## Быстрое исправление
Если проблема не решается:
1. Перезапустите игру
2. Создайте новый сервер
3. Проверьте, что вы видите зеленый квадрат
4. Попробуйте движение снова
