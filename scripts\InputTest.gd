extends Node

# InputTest - Простой тест для проверки ввода WASD

func _ready():
	print("InputTest initialized - Press WASD to test input")

func _input(event):
	if event is InputEventKey and event.pressed:
		match event.keycode:
			KEY_W:
				print("W key pressed (move_up action: ", Input.is_action_pressed("move_up"), ")")
			KEY_A:
				print("A key pressed (move_left action: ", Input.is_action_pressed("move_left"), ")")
			KEY_S:
				print("S key pressed (move_down action: ", Input.is_action_pressed("move_down"), ")")
			KEY_D:
				print("D key pressed (move_right action: ", Input.is_action_pressed("move_right"), ")")
			KEY_F1:
				test_input_actions()

func _process(_delta):
	# Проверяем действия каждый кадр
	var input_detected = false
	var input_vector = Vector2.ZERO
	
	if Input.is_action_pressed("move_up"):
		input_vector.y -= 1
		input_detected = true
	if Input.is_action_pressed("move_down"):
		input_vector.y += 1
		input_detected = true
	if Input.is_action_pressed("move_left"):
		input_vector.x -= 1
		input_detected = true
	if Input.is_action_pressed("move_right"):
		input_vector.x += 1
		input_detected = true
	
	if input_detected:
		print("Input vector: ", input_vector)

func test_input_actions():
	print("=== Input Actions Test ===")
	
	# Проверяем, существуют ли действия
	var actions = ["move_up", "move_down", "move_left", "move_right"]
	
	for action in actions:
		if InputMap.has_action(action):
			print("✓ Action '", action, "' exists")
			var events = InputMap.action_get_events(action)
			for event in events:
				if event is InputEventKey:
					print("  - Key: ", event.physical_keycode, " (", char(event.physical_keycode), ")")
		else:
			print("✗ Action '", action, "' NOT found")
	
	print("=========================")
