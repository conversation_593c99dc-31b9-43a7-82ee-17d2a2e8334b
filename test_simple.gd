extends Node

# Простой тест для проверки основных функций

func _ready():
	print("=== STALZONE ONLINE - Simple Test ===")
	
	# Тест 1: Проверка autoload'ов
	test_autoloads()
	
	# Тест 2: Проверка создания сервера
	test_server_creation()
	
	# Тест 3: Проверка загрузки сцен
	test_scene_loading()
	
	print("=== Test completed ===")

func test_autoloads():
	print("\n--- Testing Autoloads ---")
	
	if NetworkManager:
		print("✓ NetworkManager loaded")
	else:
		print("✗ NetworkManager NOT loaded")
	
	if GameManager:
		print("✓ GameManager loaded")
	else:
		print("✗ GameManager NOT loaded")
	
	if DedicatedServer:
		print("✓ DedicatedServer loaded")
	else:
		print("✗ DedicatedServer NOT loaded")

func test_server_creation():
	print("\n--- Testing Server Creation ---")
	
	if NetworkManager:
		var result = NetworkManager.create_server(7003)
		if result:
			print("✓ Server created successfully on port 7003")
			NetworkManager.disconnect_from_network()
		else:
			print("✗ Failed to create server")
	else:
		print("✗ NetworkManager not available")

func test_scene_loading():
	print("\n--- Testing Scene Loading ---")
	
	# Проверяем, можем ли мы загрузить сцены
	var main_menu_scene = load("res://scenes/MainMenu.tscn")
	if main_menu_scene:
		print("✓ MainMenu.tscn loaded")
	else:
		print("✗ MainMenu.tscn failed to load")
	
	var game_world_scene = load("res://scenes/GameWorld.tscn")
	if game_world_scene:
		print("✓ GameWorld.tscn loaded")
	else:
		print("✗ GameWorld.tscn failed to load")
	
	var player_scene = load("res://scenes/Player.tscn")
	if player_scene:
		print("✓ Player.tscn loaded")
	else:
		print("✗ Player.tscn failed to load")
