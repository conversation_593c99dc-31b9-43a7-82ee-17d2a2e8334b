extends CharacterBody2D

# Player - <PERSON>грок с top-down движением и сетевой синхронизацией
# Поддерживает как локальное управление, так и синхронизацию по сети

@onready var sprite: ColorRect = $Sprite
@onready var collision: CollisionShape2D = $CollisionShape2D
@onready var name_label: Label = $NameLabel
@onready var health_bar: ProgressBar = $HealthBar

# Параметры игрока
var player_id: int = 0
var player_name: String = ""
var max_health: float = 100.0
var current_health: float = 100.0
var move_speed: float = 200.0
var is_local_player: bool = false

# Сетевая синхронизация
var network_position: Vector2
var network_velocity: Vector2
var interpolation_speed: float = 10.0

# Управление
var input_vector: Vector2 = Vector2.ZERO

func _ready():
	# Настраиваем MultiplayerSynchronizer
	setup_multiplayer()

	# Инициализируем здоровье
	update_health_display()

	print("Player initialized: ", player_name, " (ID: ", player_id, ", Local: ", is_local_player, ")")

	# Ждем один кадр, чтобы authority был установлен
	await get_tree().process_frame

	# Теперь проверяем, является ли этот игрок локальным
	is_local_player = is_multiplayer_authority()

	# Настраиваем внешний вид после определения локального игрока
	setup_appearance()

	print("Player authority set: ", player_name, " (ID: ", player_id, ", Local: ", is_local_player, ", Authority: ", get_multiplayer_authority(), ")")

func setup_multiplayer():
	# Настраиваем синхронизацию позиции и скорости
	network_position = global_position
	network_velocity = velocity

func setup_appearance():
	# Настраиваем размер и цвет игрока
	sprite.size = Vector2(32, 32)
	sprite.position = Vector2(-16, -16)

	# Разные цвета для разных игроков
	if is_local_player:
		sprite.color = Color.GREEN
	else:
		sprite.color = Color.BLUE

	# Настраиваем отображение имени
	name_label.text = player_name
	name_label.position = Vector2(-50, -40)
	name_label.size = Vector2(100, 20)
	name_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER

	# Настраиваем полоску здоровья
	health_bar.position = Vector2(-25, 25)
	health_bar.size = Vector2(50, 8)
	health_bar.max_value = max_health
	health_bar.value = current_health

func setup_player(id: int, name: String):
	player_id = id
	player_name = name

	# Определяем локального игрока по ID
	is_local_player = (id == NetworkManager.get_local_player_id())

	# Обновляем внешний вид после установки данных
	setup_appearance()

	print("Player setup complete: ", player_name, " (ID: ", player_id, ", Local: ", is_local_player, ", Local ID: ", NetworkManager.get_local_player_id(), ")")

func _physics_process(delta):
	if is_local_player:
		handle_local_input(delta)
		handle_local_movement(delta)
		sync_to_network()
	else:
		handle_network_interpolation(delta)

func handle_local_input(_delta):
	# Получаем ввод от игрока
	input_vector = Vector2.ZERO

	if Input.is_action_pressed("move_up"):
		input_vector.y -= 1
	if Input.is_action_pressed("move_down"):
		input_vector.y += 1
	if Input.is_action_pressed("move_left"):
		input_vector.x -= 1
	if Input.is_action_pressed("move_right"):
		input_vector.x += 1

	# Отладочная информация (только первые несколько раз)
	if input_vector.length() > 0 and randf() < 0.01:  # 1% шанс вывода для уменьшения спама
		print("Input detected: ", input_vector, " for player ", player_name, " (Local: ", is_local_player, ")")

	# Нормализуем вектор для диагонального движения
	if input_vector.length() > 0:
		input_vector = input_vector.normalized()

func handle_local_movement(delta):
	# Применяем движение
	velocity = input_vector * move_speed
	move_and_slide()
	
	# Обновляем сетевую позицию
	network_position = global_position
	network_velocity = velocity

func sync_to_network():
	# Отправляем позицию другим игрокам
	if is_local_player:
		NetworkManager.update_player_position.rpc_unreliable(player_id, global_position)

func handle_network_interpolation(delta):
	# Интерполируем к сетевой позиции для плавности
	global_position = global_position.lerp(network_position, interpolation_speed * delta)

# Сетевые RPC функции
@rpc("any_peer", "unreliable")
func sync_position(pos: Vector2):
	if not is_local_player:
		network_position = pos

# Обработка обновления позиции от NetworkManager
func _on_position_updated(new_position: Vector2):
	if not is_local_player:
		network_position = new_position

@rpc("any_peer", "reliable")
func sync_health(health: float):
	current_health = health
	update_health_display()

@rpc("any_peer", "reliable")
func take_damage(damage: float):
	if is_local_player:
		current_health = max(0, current_health - damage)
		update_health_display()
		sync_health.rpc(current_health)
		
		if current_health <= 0:
			die()

func die():
	print("Player died: ", player_name)
	# Здесь можно добавить логику смерти
	# Например, респавн через некоторое время

func heal(amount: float):
	if is_local_player:
		current_health = min(max_health, current_health + amount)
		update_health_display()
		sync_health.rpc(current_health)

func update_health_display():
	health_bar.value = current_health
	
	# Меняем цвет полоски здоровья в зависимости от уровня
	var health_percent = current_health / max_health
	if health_percent > 0.6:
		health_bar.modulate = Color.GREEN
	elif health_percent > 0.3:
		health_bar.modulate = Color.YELLOW
	else:
		health_bar.modulate = Color.RED

func _input(event):
	if not is_local_player:
		return
	
	if event is InputEventKey and event.pressed:
		if event.keycode == KEY_ESCAPE:
			GameManager.quit_to_menu()

# Получение информации об игроке
func get_player_info() -> Dictionary:
	return {
		"id": player_id,
		"name": player_name,
		"position": global_position,
		"health": current_health,
		"max_health": max_health
	}

# Установка позиции (для спавна)
func set_spawn_position(pos: Vector2):
	global_position = pos
	network_position = pos
